#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试内存分配错误修复
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
current_dir = Path(__file__).resolve().parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from tasks.daily_once.quant_stock_trading_signals import FastIndicatorCalculator, BatchProcessor
from utils.logger import logger

def test_safe_array_conversion():
    """测试安全数组转换功能"""
    print("=" * 50)
    print("测试安全数组转换功能")
    print("=" * 50)
    
    calculator = FastIndicatorCalculator()
    
    # 测试正常数据
    normal_data = tuple(range(1, 101))  # 1-100的正常数据
    result = calculator._safe_array_conversion(normal_data, "close")
    print(f"正常数据测试: 输入长度={len(normal_data)}, 输出长度={len(result) if result is not None else 0}")
    
    # 测试异常大的数据
    large_data = tuple([1e15] * 1000)  # 异常大的数值
    result = calculator._safe_array_conversion(large_data, "close")
    print(f"异常大数值测试: 输入长度={len(large_data)}, 输出={result is not None}")
    
    # 测试异常长的数据
    long_data = tuple(range(1, 100001))  # 10万个数据点
    result = calculator._safe_array_conversion(long_data, "close")
    print(f"异常长数据测试: 输入长度={len(long_data)}, 输出长度={len(result) if result is not None else 0}")
    
    # 测试包含NaN和无穷大的数据
    bad_data = (1.0, 2.0, np.inf, 4.0, np.nan, 6.0, -np.inf, 8.0)
    result = calculator._safe_array_conversion(bad_data, "close")
    print(f"异常值数据测试: 输入长度={len(bad_data)}, 输出长度={len(result) if result is not None else 0}")
    
    # 测试负价格数据
    negative_data = (-1.0, 0.0, 1.0, -5.0, 10.0)
    result = calculator._safe_array_conversion(negative_data, "close")
    print(f"负价格数据测试: 输入长度={len(negative_data)}, 输出长度={len(result) if result is not None else 0}")
    
    print("✅ 安全数组转换测试完成\n")

def test_safe_convert_to_tuple():
    """测试安全Series转换功能"""
    print("=" * 50)
    print("测试安全Series转换功能")
    print("=" * 50)
    
    calculator = FastIndicatorCalculator()
    
    # 测试正常Series
    normal_series = pd.Series(range(1, 101), name='close')
    result = calculator._safe_convert_to_tuple(normal_series)
    print(f"正常Series测试: 输入长度={len(normal_series)}, 输出长度={len(result)}")
    
    # 测试包含异常值的Series
    bad_series = pd.Series([1.0, 2.0, np.inf, 4.0, np.nan, 6.0, -np.inf, 8.0], name='close')
    result = calculator._safe_convert_to_tuple(bad_series)
    print(f"异常值Series测试: 输入长度={len(bad_series)}, 输出长度={len(result)}")
    
    # 测试异常长的Series
    long_series = pd.Series(range(1, 100001), name='close')
    result = calculator._safe_convert_to_tuple(long_series)
    print(f"异常长Series测试: 输入长度={len(long_series)}, 输出长度={len(result)}")
    
    # 测试异常大数值的Series
    large_series = pd.Series([1e15] * 1000, name='close')
    result = calculator._safe_convert_to_tuple(large_series)
    print(f"异常大数值Series测试: 输入长度={len(large_series)}, 输出长度={len(result)}")
    
    print("✅ 安全Series转换测试完成\n")

def test_problematic_stock_handling():
    """测试问题股票处理"""
    print("=" * 50)
    print("测试问题股票处理")
    print("=" * 50)
    
    processor = BatchProcessor()
    
    # 检查问题股票列表
    print(f"已知问题股票: {processor.problematic_stocks}")
    
    # 测试是否会跳过问题股票
    test_codes = ['000001', '603223', '000002']
    print(f"测试股票代码: {test_codes}")
    
    for code in test_codes:
        if code in processor.problematic_stocks:
            print(f"✅ 股票 {code} 被正确识别为问题股票")
        else:
            print(f"✓ 股票 {code} 正常处理")
    
    print("✅ 问题股票处理测试完成\n")

def test_special_clean_function():
    """测试特殊清理功能"""
    print("=" * 50)
    print("测试特殊清理功能")
    print("=" * 50)
    
    processor = BatchProcessor()
    
    # 创建测试数据
    test_data = {
        'open': [1.0, 2.0, 3.0, 1e10, 5.0],  # 包含异常大值
        'high': [1.1, 2.1, 3.1, 4.1, 5.1],
        'low': [0.9, 1.9, 2.9, 3.9, 4.9],
        'close': [1.05, 2.05, 3.05, 4.05, 5.05],
        'volume': [1000, 2000, 3000, 4000, 5000],
        'tradedate': ['20250101', '20250102', '20250103', '20250104', '20250105']
    }
    
    df = pd.DataFrame(test_data)
    print(f"原始测试数据长度: {len(df)}")
    print(f"原始数据最大值: open={df['open'].max()}")
    
    # 测试特殊清理
    cleaned_df = processor._special_clean_problematic_stock(df, '603223')
    
    if cleaned_df is not None:
        print(f"清理后数据长度: {len(cleaned_df)}")
        print(f"清理后数据最大值: open={cleaned_df['open'].max()}")
        print("✅ 特殊清理功能正常")
    else:
        print("❌ 特殊清理返回None")
    
    print("✅ 特殊清理功能测试完成\n")

def main():
    """主测试函数"""
    print("开始测试内存分配错误修复...")
    print()
    
    try:
        test_safe_array_conversion()
        test_safe_convert_to_tuple()
        test_problematic_stock_handling()
        test_special_clean_function()
        
        print("=" * 50)
        print("✅ 所有测试完成，修复功能正常工作")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
